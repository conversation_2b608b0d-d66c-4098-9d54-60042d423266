"""
FastAPI application factory
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from ..core.config import Config
from ..core.logging import setup_logging, get_logger
from ..core.env_manager import EnvironmentManager
from .endpoints import agentic, dashboard


def create_app(config: Config = None) -> FastAPI:

    if config is None:
        config = Config.from_env()

    # Setup environment
    env_manager = EnvironmentManager()
    env_manager.create_logs_directory()

    # Setup logging
    setup_logging()
    logger = get_logger("api")

    # Validate configuration
    config.validate()
    logger.info(f"Configuration validated: {config}")

    # Validate environment
    validation = env_manager.validate_environment()
    if not validation['valid']:
        logger.warning(f"Environment validation issues: {validation['errors']}")
    
    # Create FastAPI app
    app = FastAPI(
        title="Accela Knowledge Base - ASK API",
        description="Natural language interface for Accela implementation queries",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware with explicit frontend origins for development
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Store config in app state
    app.state.config = config
    
    # Include routers - ASK endpoint and Dashboard
    app.include_router(agentic.router, prefix="/agentic", tags=["agentic"])
    app.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize system on startup"""
        logger.info("Starting Accela Knowledge Base API")
        
        # Initialize knowledge graph and orchestrator (only what's needed for ASK endpoint)
        from ..knowledge_graph.graph_builder import AccelaKnowledgeGraph
        from ..core.dynamic_orchestrator import DynamicOrchestrator

        # Build knowledge graph once during startup
        knowledge_graph = AccelaKnowledgeGraph(config)
        knowledge_graph.build_from_metadata()

        # Initialize dynamic orchestrator (used by ASK endpoint)
        dynamic_orchestrator = DynamicOrchestrator(config)

        # Store in app state
        app.state.knowledge_graph = knowledge_graph
        app.state.dynamic_orchestrator = dynamic_orchestrator

        logger.info("System initialized successfully - ASK endpoint ready")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup on shutdown"""
        logger.info("Shutting down Accela Knowledge Base API")
    
    return app
