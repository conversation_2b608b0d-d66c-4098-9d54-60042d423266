"""
Smart Dashboard endpoints for Accela implementations
"""

from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import json
import asyncio
from datetime import datetime, timedelta
import random

from ...core.logging import get_logger

router = APIRouter()
logger = get_logger("dashboard")


# ===== REQUEST/RESPONSE MODELS =====
class DashboardFilters(BaseModel):
    counties: List[str] = []
    start_date: str
    end_date: str
    base_date: str
    categories: List[str] = []
    subcategories: List[str] = []
    departments: List[str] = []


class SmartDashboardRequest(BaseModel):
    filters: DashboardFilters
    user_query: str = ""
    use_default_charts: bool = True
    dashboard_type: str = "permits"


class SummaryItem(BaseModel):
    icon: str
    value: str
    label: str
    data_type: str
    secondary_info: Optional[str] = None
    formula: Optional[str] = None


class ChartDataset(BaseModel):
    label: str
    data: List[float]
    backgroundColor: List[str] = []
    borderColor: List[str] = []


class ChartData(BaseModel):
    labels: List[str]
    datasets: List[ChartDataset]
    headers: Optional[List[str]] = None  # For table charts
    rows: Optional[List[List[Any]]] = None  # For table charts


class Chart(BaseModel):
    id: str
    title: str
    type: str
    data: ChartData
    subtitle: Optional[str] = None
    size: Optional[str] = "medium"
    options: Optional[Dict[str, Any]] = None


class DashboardResponse(BaseModel):
    charts: List[Chart]
    summary_items: List[SummaryItem]


class SmartDashboardApiResponse(BaseModel):
    status: str
    data: Optional[DashboardResponse] = None
    message: Optional[str] = None
    error_code: Optional[str] = None


# ===== DASHBOARD CONFIGURATION =====
DASHBOARD_CONFIG = {
    "chart_colors": [
        "#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6",
        "#EC4899", "#06B6D4", "#84CC16", "#F97316", "#6366F1"
    ],
    "chart_types": {
        "bar": "Bar Chart",
        "line": "Line Chart",
        "pie": "Pie Chart",
        "doughnut": "Doughnut Chart",
        "area": "Area Chart",
        "table": "Data Table"
    },
    "currency": {
        "code": "USD",
        "symbol": "$"
    },
    "dashboard_types": [
        {
            "value": "permits",
            "label": "Building Permits",
            "icon": "building",
            "description": "Building permit applications, approvals, and inspections"
        },
        {
            "value": "inspections",
            "label": "Inspections",
            "icon": "search",
            "description": "Code compliance and safety inspections"
        },
        {
            "value": "code_enforcement",
            "label": "Code Enforcement",
            "icon": "shield",
            "description": "Code violations and enforcement actions"
        },
        {
            "value": "licensing",
            "label": "Business Licensing",
            "icon": "certificate",
            "description": "Business licenses and renewals"
        }
    ],
    "base_date_options": [
        {"value": "application_date", "label": "Application Date"},
        {"value": "issued_date", "label": "Issued Date"},
        {"value": "inspection_date", "label": "Inspection Date"},
        {"value": "completion_date", "label": "Completion Date"}
    ],
    "default_chart_options": {
        "responsive": True,
        "maintainAspectRatio": False,
        "plugins": {
            "legend": {
                "position": "bottom"
            }
        }
    },
    "summary_card_config": {
        "colors": {
            "currency": "#10B981",
            "number": "#3B82F6", 
            "percentage": "#F59E0B",
            "text": "#6B7280",
            "days": "#8B5CF6"
        },
        "icons": {
            "currency": "dollar-sign",
            "number": "hash",
            "percentage": "percent",
            "text": "info",
            "days": "calendar"
        }
    },
    "ui_config": {
        "default_date_range_days": 30,
        "default_dashboard_type": "permits",
        "default_base_date": "application_date"
    }
}


# ===== ROUTES =====
@router.get("/config")
async def get_dashboard_config() -> Dict[str, Any]:
    """Get global dashboard configuration for dynamic frontend rendering"""
    return {"status": "success", "data": DASHBOARD_CONFIG}


@router.post("/smart_ask")
async def smart_ask(request: SmartDashboardRequest, app_request: Request) -> StreamingResponse:
    """Smart dashboard with natural language queries and default charts"""
    
    async def event_stream():
        try:
            # Step 1: Query Analysis
            yield f"data: {json.dumps({'event_type': 'query_analysis_start', 'data': 'Analyzing dashboard request...'})}\n\n"
            await asyncio.sleep(0.5)
            
            # Step 2: Data Processing
            yield f"data: {json.dumps({'event_type': 'data_processing_start', 'data': 'Processing Accela data...'})}\n\n"
            await asyncio.sleep(1)
            
            # Generate dashboard data based on request
            dashboard_data = await generate_dashboard_data(request)
            
            # Step 3: Chart Generation
            yield f"data: {json.dumps({'event_type': 'chart_generation_start', 'data': 'Generating charts and insights...'})}\n\n"
            await asyncio.sleep(0.5)
            
            # Step 4: Complete with data
            response_data = {
                'event_type': 'dashboard_complete',
                'data': dashboard_data.dict()
            }
            yield f"data: {json.dumps(response_data)}\n\n"
            
            # Final completion
            yield f"data: {json.dumps({'event_type': 'complete', 'data': 'Dashboard generated successfully.'})}\n\n"
            
        except Exception as e:
            logger.error(f"Error in smart_ask: {str(e)}")
            error_response = {
                'event_type': 'error',
                'data': f'Error generating dashboard: {str(e)}'
            }
            yield f"data: {json.dumps(error_response)}\n\n"
    
    return StreamingResponse(event_stream(), media_type="text/event-stream")


@router.post("/default")
async def get_default_dashboard(request: SmartDashboardRequest) -> SmartDashboardApiResponse:
    """Get default dashboard data without streaming"""
    try:
        dashboard_data = await generate_dashboard_data(request)
        return SmartDashboardApiResponse(
            status="success",
            data=dashboard_data
        )
    except Exception as e:
        logger.error(f"Error in get_default_dashboard: {str(e)}")
        return SmartDashboardApiResponse(
            status="error",
            message=str(e),
            error_code="DASHBOARD_ERROR"
        )


# ===== HELPER FUNCTIONS =====
async def generate_dashboard_data(request: SmartDashboardRequest) -> DashboardResponse:
    """Generate dashboard data based on request parameters"""
    
    dashboard_type = request.dashboard_type
    
    if dashboard_type == "permits":
        return await generate_permits_dashboard(request)
    elif dashboard_type == "inspections":
        return await generate_inspections_dashboard(request)
    elif dashboard_type == "code_enforcement":
        return await generate_code_enforcement_dashboard(request)
    elif dashboard_type == "licensing":
        return await generate_licensing_dashboard(request)
    else:
        # Default to permits
        return await generate_permits_dashboard(request)


async def generate_permits_dashboard(request: SmartDashboardRequest) -> DashboardResponse:
    """Generate permits dashboard with dummy data"""
    
    # Dummy summary items
    summary_items = [
        SummaryItem(
            icon="building",
            value="1,247",
            label="Total Permits",
            data_type="number",
            secondary_info="+12% from last month"
        ),
        SummaryItem(
            icon="dollar-sign",
            value="$2,847,500",
            label="Permit Revenue",
            data_type="currency",
            secondary_info="+8% from last month"
        ),
        SummaryItem(
            icon="calendar",
            value="14.2",
            label="Avg Processing Days",
            data_type="days",
            secondary_info="-2.1 days improvement"
        ),
        SummaryItem(
            icon="percent",
            value="87.5%",
            label="Approval Rate",
            data_type="percentage",
            secondary_info="+3.2% from last month"
        )
    ]
    
    # Dummy charts
    charts = [
        Chart(
            id="permits_by_type",
            title="Permits by Type",
            type="bar",
            data=ChartData(
                labels=["Residential", "Commercial", "Industrial", "Renovation"],
                datasets=[ChartDataset(
                    label="Permit Count",
                    data=[456, 234, 89, 468],
                    backgroundColor=DASHBOARD_CONFIG["chart_colors"][:4]
                )]
            ),
            size="medium"
        ),
        Chart(
            id="monthly_trend",
            title="Monthly Permit Trend",
            type="line",
            data=ChartData(
                labels=["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                datasets=[ChartDataset(
                    label="Permits Issued",
                    data=[180, 195, 210, 185, 220, 247],
                    borderColor=[DASHBOARD_CONFIG["chart_colors"][0]]
                )]
            ),
            size="large"
        )
    ]
    
    return DashboardResponse(
        summary_items=summary_items,
        charts=charts
    )


async def generate_inspections_dashboard(request: SmartDashboardRequest) -> DashboardResponse:
    """Generate inspections dashboard with dummy data"""

    summary_items = [
        SummaryItem(
            icon="search",
            value="2,156",
            label="Total Inspections",
            data_type="number",
            secondary_info="+18% from last month"
        ),
        SummaryItem(
            icon="check-circle",
            value="1,847",
            label="Passed Inspections",
            data_type="number",
            secondary_info="85.7% pass rate"
        ),
        SummaryItem(
            icon="calendar",
            value="3.2",
            label="Avg Response Days",
            data_type="days",
            secondary_info="-0.8 days improvement"
        ),
        SummaryItem(
            icon="x-circle",
            value="309",
            label="Failed Inspections",
            data_type="number",
            secondary_info="14.3% failure rate"
        )
    ]

    charts = [
        Chart(
            id="inspection_results",
            title="Inspection Results",
            type="doughnut",
            data=ChartData(
                labels=["Passed", "Failed", "Pending"],
                datasets=[ChartDataset(
                    label="Inspections",
                    data=[1847, 309, 156],
                    backgroundColor=["#10B981", "#EF4444", "#F59E0B"]
                )]
            ),
            size="medium"
        ),
        Chart(
            id="inspection_types",
            title="Inspections by Type",
            type="bar",
            data=ChartData(
                labels=["Electrical", "Plumbing", "Structural", "HVAC", "Fire Safety"],
                datasets=[ChartDataset(
                    label="Count",
                    data=[456, 389, 234, 178, 123],
                    backgroundColor=DASHBOARD_CONFIG["chart_colors"][:5]
                )]
            ),
            size="large"
        )
    ]

    return DashboardResponse(
        summary_items=summary_items,
        charts=charts
    )


async def generate_code_enforcement_dashboard(request: SmartDashboardRequest) -> DashboardResponse:
    """Generate code enforcement dashboard with dummy data"""

    summary_items = [
        SummaryItem(
            icon="shield",
            value="456",
            label="Active Cases",
            data_type="number",
            secondary_info="-23 from last month"
        ),
        SummaryItem(
            icon="alert-triangle",
            value="89",
            label="New Violations",
            data_type="number",
            secondary_info="+12% from last month"
        ),
        SummaryItem(
            icon="calendar",
            value="21.5",
            label="Avg Resolution Days",
            data_type="days",
            secondary_info="-3.2 days improvement"
        ),
        SummaryItem(
            icon="percent",
            value="78.3%",
            label="Compliance Rate",
            data_type="percentage",
            secondary_info="+5.1% from last month"
        )
    ]

    charts = [
        Chart(
            id="violation_types",
            title="Violations by Type",
            type="pie",
            data=ChartData(
                labels=["Property Maintenance", "Zoning", "Building Code", "Health & Safety"],
                datasets=[ChartDataset(
                    label="Violations",
                    data=[234, 156, 89, 67],
                    backgroundColor=DASHBOARD_CONFIG["chart_colors"][:4]
                )]
            ),
            size="medium"
        ),
        Chart(
            id="case_status",
            title="Case Status Overview",
            type="bar",
            data=ChartData(
                labels=["Open", "In Progress", "Resolved", "Closed"],
                datasets=[ChartDataset(
                    label="Cases",
                    data=[156, 234, 345, 123],
                    backgroundColor=["#EF4444", "#F59E0B", "#10B981", "#6B7280"]
                )]
            ),
            size="large"
        )
    ]

    return DashboardResponse(
        summary_items=summary_items,
        charts=charts
    )


async def generate_licensing_dashboard(request: SmartDashboardRequest) -> DashboardResponse:
    """Generate licensing dashboard with dummy data"""

    summary_items = [
        SummaryItem(
            icon="certificate",
            value="3,456",
            label="Active Licenses",
            data_type="number",
            secondary_info="+156 new this month"
        ),
        SummaryItem(
            icon="dollar-sign",
            value="$456,780",
            label="License Revenue",
            data_type="currency",
            secondary_info="+15% from last month"
        ),
        SummaryItem(
            icon="calendar",
            value="234",
            label="Expiring Soon",
            data_type="number",
            secondary_info="Next 30 days"
        ),
        SummaryItem(
            icon="percent",
            value="92.1%",
            label="Renewal Rate",
            data_type="percentage",
            secondary_info="+2.3% from last year"
        )
    ]

    charts = [
        Chart(
            id="license_types",
            title="Licenses by Type",
            type="bar",
            data=ChartData(
                labels=["Business", "Food Service", "Liquor", "Professional", "Contractor"],
                datasets=[ChartDataset(
                    label="Active Licenses",
                    data=[1234, 567, 234, 456, 789],
                    backgroundColor=DASHBOARD_CONFIG["chart_colors"][:5]
                )]
            ),
            size="large"
        ),
        Chart(
            id="renewal_trend",
            title="License Renewals Trend",
            type="line",
            data=ChartData(
                labels=["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                datasets=[ChartDataset(
                    label="Renewals",
                    data=[89, 95, 102, 87, 110, 123],
                    borderColor=[DASHBOARD_CONFIG["chart_colors"][2]]
                )]
            ),
            size="medium"
        )
    ]

    return DashboardResponse(
        summary_items=summary_items,
        charts=charts
    )
