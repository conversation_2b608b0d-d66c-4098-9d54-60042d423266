"""
Smart Dashboard endpoints for Accela implementations
"""

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from typing import Dict, Any
import json
import asyncio

from ...core.logging import get_logger
from ..models.dashboard import SmartDashboardRequest, SmartDashboardApiResponse
from ..config.dashboard_config import DASH<PERSON><PERSON>D_CONFIG
from ..services.dashboard_generator import DashboardGenerator

router = APIRouter()
logger = get_logger("dashboard")



@router.get("/config")
async def get_dashboard_config() -> Dict[str, Any]:
    return {"status": "success", "data": DASHBOARD_CONFIG}


@router.post("/smart_ask")
async def smart_ask(request: SmartDashboardRequest) -> StreamingResponse:
    async def event_stream():
        try:
            yield f"data: {json.dumps({'event_type': 'query_analysis_start', 'data': 'Analyzing dashboard request...'})}\n\n"
            await asyncio.sleep(0.5)

            yield f"data: {json.dumps({'event_type': 'data_processing_start', 'data': 'Processing Accela data...'})}\n\n"
            await asyncio.sleep(1)

            dashboard_data = await generate_dashboard_data(request)

            yield f"data: {json.dumps({'event_type': 'chart_generation_start', 'data': 'Generating charts and insights...'})}\n\n"
            await asyncio.sleep(0.5)

            response_data = {
                'event_type': 'dashboard_complete',
                'data': dashboard_data.dict()
            }
            yield f"data: {json.dumps(response_data)}\n\n"

            yield f"data: {json.dumps({'event_type': 'complete', 'data': 'Dashboard generated successfully.'})}\n\n"

        except Exception as e:
            logger.error(f"Error in smart_ask: {str(e)}")
            error_response = {
                'event_type': 'error',
                'data': f'Error generating dashboard: {str(e)}'
            }
            yield f"data: {json.dumps(error_response)}\n\n"

    return StreamingResponse(event_stream(), media_type="text/event-stream")


@router.post("/default")
async def get_default_dashboard(request: SmartDashboardRequest) -> SmartDashboardApiResponse:
    try:
        dashboard_data = await generate_dashboard_data(request)
        return SmartDashboardApiResponse(
            status="success",
            data=dashboard_data
        )
    except Exception as e:
        logger.error(f"Error in get_default_dashboard: {str(e)}")
        return SmartDashboardApiResponse(
            status="error",
            message=str(e),
            error_code="DASHBOARD_ERROR"
        )


async def generate_dashboard_data(request: SmartDashboardRequest):
    dashboard_type = request.dashboard_type

    if dashboard_type == "permits":
        return await DashboardGenerator.generate_permits_dashboard(request)
    elif dashboard_type == "inspections":
        return await DashboardGenerator.generate_inspections_dashboard(request)
    elif dashboard_type == "code_enforcement":
        return await DashboardGenerator.generate_code_enforcement_dashboard(request)
    elif dashboard_type == "licensing":
        return await DashboardGenerator.generate_licensing_dashboard(request)
    else:
        return await DashboardGenerator.generate_permits_dashboard(request)




