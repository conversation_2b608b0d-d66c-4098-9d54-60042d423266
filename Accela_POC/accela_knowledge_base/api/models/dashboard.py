"""
Dashboard data models for Accela implementations
"""

from pydantic import BaseModel
from typing import Dict, Any, List, Optional


class DashboardFilters(BaseModel):
    counties: List[str] = []
    start_date: str
    end_date: str
    base_date: str
    categories: List[str] = []
    subcategories: List[str] = []
    departments: List[str] = []


class SmartDashboardRequest(BaseModel):
    filters: DashboardFilters
    user_query: str = ""
    use_default_charts: bool = True
    dashboard_type: str = "permits"


class SummaryItem(BaseModel):
    icon: str
    value: str
    label: str
    data_type: str
    secondary_info: Optional[str] = None
    formula: Optional[str] = None


class ChartDataset(BaseModel):
    label: str
    data: List[float]
    backgroundColor: List[str] = []
    borderColor: List[str] = []


class ChartData(BaseModel):
    labels: List[str]
    datasets: List[ChartDataset]
    headers: Optional[List[str]] = None
    rows: Optional[List[List[Any]]] = None


class Chart(BaseModel):
    id: str
    title: str
    type: str
    data: ChartData
    subtitle: Optional[str] = None
    size: Optional[str] = "medium"
    options: Optional[Dict[str, Any]] = None


class DashboardResponse(BaseModel):
    charts: List[Chart]
    summary_items: List[SummaryItem]


class SmartDashboardApiResponse(BaseModel):
    status: str
    data: Optional[DashboardResponse] = None
    message: Optional[str] = None
    error_code: Optional[str] = None
