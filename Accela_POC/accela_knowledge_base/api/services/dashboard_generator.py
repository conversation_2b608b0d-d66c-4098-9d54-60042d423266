"""
Dashboard data generation services
"""

from typing import Dict, List
from ..models.dashboard import DashboardResponse, SummaryItem, Chart, ChartData, ChartDataset, SmartDashboardRequest
from ..config.dashboard_config import DASHBOARD_CONFIG


class DashboardGenerator:
    
    @staticmethod
    async def generate_permits_dashboard(request: SmartDashboardRequest) -> DashboardResponse:
        summary_items = [
            SummaryItem(
                icon="building",
                value="1,247",
                label="Total Permits",
                data_type="number",
                secondary_info="+12% from last month"
            ),
            SummaryItem(
                icon="dollar-sign",
                value="$2,847,500",
                label="Permit Revenue",
                data_type="currency",
                secondary_info="+8% from last month"
            ),
            SummaryItem(
                icon="calendar",
                value="14.2",
                label="Avg Processing Days",
                data_type="days",
                secondary_info="-2.1 days improvement"
            ),
            SummaryItem(
                icon="percent",
                value="87.5%",
                label="Approval Rate",
                data_type="percentage",
                secondary_info="+3.2% from last month"
            )
        ]
        
        charts = [
            Chart(
                id="permits_by_type",
                title="Permits by Type",
                type="bar",
                data=ChartData(
                    labels=["Residential", "Commercial", "Industrial", "Renovation"],
                    datasets=[ChartDataset(
                        label="Permit Count",
                        data=[456, 234, 89, 468],
                        backgroundColor=DASHBOARD_CONFIG["chart_colors"][:4]
                    )]
                ),
                size="medium"
            ),
            Chart(
                id="monthly_trend",
                title="Monthly Permit Trend",
                type="line",
                data=ChartData(
                    labels=["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                    datasets=[ChartDataset(
                        label="Permits Issued",
                        data=[180, 195, 210, 185, 220, 247],
                        borderColor=[DASHBOARD_CONFIG["chart_colors"][0]]
                    )]
                ),
                size="large"
            )
        ]
        
        return DashboardResponse(summary_items=summary_items, charts=charts)

    @staticmethod
    async def generate_inspections_dashboard(request: SmartDashboardRequest) -> DashboardResponse:
        summary_items = [
            SummaryItem(
                icon="search",
                value="2,156",
                label="Total Inspections",
                data_type="number",
                secondary_info="+18% from last month"
            ),
            SummaryItem(
                icon="check-circle",
                value="1,847",
                label="Passed Inspections",
                data_type="number",
                secondary_info="85.7% pass rate"
            ),
            SummaryItem(
                icon="calendar",
                value="3.2",
                label="Avg Response Days",
                data_type="days",
                secondary_info="-0.8 days improvement"
            ),
            SummaryItem(
                icon="x-circle",
                value="309",
                label="Failed Inspections",
                data_type="number",
                secondary_info="14.3% failure rate"
            )
        ]
        
        charts = [
            Chart(
                id="inspection_results",
                title="Inspection Results",
                type="doughnut",
                data=ChartData(
                    labels=["Passed", "Failed", "Pending"],
                    datasets=[ChartDataset(
                        label="Inspections",
                        data=[1847, 309, 156],
                        backgroundColor=["#10B981", "#EF4444", "#F59E0B"]
                    )]
                ),
                size="medium"
            ),
            Chart(
                id="inspection_types",
                title="Inspections by Type",
                type="bar",
                data=ChartData(
                    labels=["Electrical", "Plumbing", "Structural", "HVAC", "Fire Safety"],
                    datasets=[ChartDataset(
                        label="Count",
                        data=[456, 389, 234, 178, 123],
                        backgroundColor=DASHBOARD_CONFIG["chart_colors"][:5]
                    )]
                ),
                size="large"
            )
        ]
        
        return DashboardResponse(summary_items=summary_items, charts=charts)

    @staticmethod
    async def generate_code_enforcement_dashboard(request: SmartDashboardRequest) -> DashboardResponse:
        summary_items = [
            SummaryItem(
                icon="shield",
                value="456",
                label="Active Cases",
                data_type="number",
                secondary_info="-23 from last month"
            ),
            SummaryItem(
                icon="alert-triangle",
                value="89",
                label="New Violations",
                data_type="number",
                secondary_info="+12% from last month"
            ),
            SummaryItem(
                icon="calendar",
                value="21.5",
                label="Avg Resolution Days",
                data_type="days",
                secondary_info="-3.2 days improvement"
            ),
            SummaryItem(
                icon="percent",
                value="78.3%",
                label="Compliance Rate",
                data_type="percentage",
                secondary_info="+5.1% from last month"
            )
        ]
        
        charts = [
            Chart(
                id="violation_types",
                title="Violations by Type",
                type="pie",
                data=ChartData(
                    labels=["Property Maintenance", "Zoning", "Building Code", "Health & Safety"],
                    datasets=[ChartDataset(
                        label="Violations",
                        data=[234, 156, 89, 67],
                        backgroundColor=DASHBOARD_CONFIG["chart_colors"][:4]
                    )]
                ),
                size="medium"
            ),
            Chart(
                id="case_status",
                title="Case Status Overview",
                type="bar",
                data=ChartData(
                    labels=["Open", "In Progress", "Resolved", "Closed"],
                    datasets=[ChartDataset(
                        label="Cases",
                        data=[156, 234, 345, 123],
                        backgroundColor=["#EF4444", "#F59E0B", "#10B981", "#6B7280"]
                    )]
                ),
                size="large"
            )
        ]
        
        return DashboardResponse(summary_items=summary_items, charts=charts)

    @staticmethod
    async def generate_licensing_dashboard(request: SmartDashboardRequest) -> DashboardResponse:
        summary_items = [
            SummaryItem(
                icon="certificate",
                value="3,456",
                label="Active Licenses",
                data_type="number",
                secondary_info="+156 new this month"
            ),
            SummaryItem(
                icon="dollar-sign",
                value="$456,780",
                label="License Revenue",
                data_type="currency",
                secondary_info="+15% from last month"
            ),
            SummaryItem(
                icon="calendar",
                value="234",
                label="Expiring Soon",
                data_type="number",
                secondary_info="Next 30 days"
            ),
            SummaryItem(
                icon="percent",
                value="92.1%",
                label="Renewal Rate",
                data_type="percentage",
                secondary_info="+2.3% from last year"
            )
        ]
        
        charts = [
            Chart(
                id="license_types",
                title="Licenses by Type",
                type="bar",
                data=ChartData(
                    labels=["Business", "Food Service", "Liquor", "Professional", "Contractor"],
                    datasets=[ChartDataset(
                        label="Active Licenses",
                        data=[1234, 567, 234, 456, 789],
                        backgroundColor=DASHBOARD_CONFIG["chart_colors"][:5]
                    )]
                ),
                size="large"
            ),
            Chart(
                id="renewal_trend",
                title="License Renewals Trend",
                type="line",
                data=ChartData(
                    labels=["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                    datasets=[ChartDataset(
                        label="Renewals",
                        data=[89, 95, 102, 87, 110, 123],
                        borderColor=[DASHBOARD_CONFIG["chart_colors"][2]]
                    )]
                ),
                size="medium"
            )
        ]
        
        return DashboardResponse(summary_items=summary_items, charts=charts)
