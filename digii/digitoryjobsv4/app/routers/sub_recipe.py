from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.database import mappingCol
import os
from bson import ObjectId
from fastapi import Query

router = APIRouter()
security = HTTPBearer()
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")
    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials.credentials

@router.get('/List')
def List(tenantId: str, export: bool ,itemCode: str,page: int = Query(1, ge=1), per_page: int = Query(10, ge=1), token: str = Depends(authenticate)):
    if export == True:
        mappingList = list(mappingCol.find({"tenantId": tenantId,"itemCode":itemCode}).sort([("_id", -1)]))
    else :
        skip = (page - 1) * per_page
        mappingList = list(mappingCol.find({"tenantId": tenantId,"itemCode":itemCode}).sort([("_id", -1)]).skip(skip).limit(per_page))
    count = mappingCol.count_documents({"tenantId": tenantId,"itemCode":itemCode})
    for doc in mappingList:
        doc['_id'] = str(doc['_id'])
    return {"status": True, "message": "Listed Successfully", "data": mappingList,"count" : count}

