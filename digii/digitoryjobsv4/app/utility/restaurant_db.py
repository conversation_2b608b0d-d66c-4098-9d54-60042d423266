"""
Database utility functions for restaurant data.
"""
import os
from typing import List, Optional, Dict, Any
from sqlalchemy import text, create_engine
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()
engine = create_engine(os.getenv("POSTGRES_URL"))

async def initialize_restaurant_tables():
    """
    Initialize the restaurant data tables if they don't exist.
    """
    try:
        # Try to find the SQL script in different locations
        script_paths = [
            "scripts/create_restaurant_data_table.sql",
            "digitoryjobsv4/scripts/create_restaurant_data_table.sql",
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "scripts/create_restaurant_data_table.sql")
        ]

        sql_script = None
        for path in script_paths:
            if os.path.exists(path):
                with open(path, "r") as f:
                    sql_script = f.read()
                break

        if sql_script is None:
            print(f"Warning: Could not find restaurant data SQL script. Current directory: {os.getcwd()}")
            # Create the table directly if script not found
            sql_script = """
            -- Create restaurant_data table
            CREATE TABLE IF NOT EXISTS restaurant_data (
                id SERIAL PRIMARY KEY,
                tenant_id VARCHAR(255) NOT NULL,
                data JSONB NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );

            -- Create index for faster queries by tenant_id
            CREATE INDEX IF NOT EXISTS idx_restaurant_tenant_id ON restaurant_data (tenant_id);

            -- Create a function to update the updated_at timestamp
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            -- Create a trigger to automatically update the updated_at column
            DROP TRIGGER IF EXISTS update_restaurant_data_updated_at ON restaurant_data;
            CREATE TRIGGER update_restaurant_data_updated_at
            BEFORE UPDATE ON restaurant_data
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
            """

        with engine.connect() as conn:
            conn.execute(text(sql_script))
            conn.commit()
            print("Restaurant data tables initialized successfully")
    except Exception as e:
        print(f"Error initializing restaurant data tables: {str(e)}")

async def save_restaurant_data(tenant_id: str, data: Dict[str, Any]) -> int:
    """
    Save restaurant data to the database.
    If data for the tenant already exists, it will be updated.

    Args:
        tenant_id: The tenant ID
        data: The restaurant data as a dictionary

    Returns:
        The ID of the inserted/updated record
    """
    # Check if data already exists for this tenant
    check_query = text("""
        SELECT id FROM restaurant_data
        WHERE tenant_id = :tenant_id
    """)

    try:
        with engine.connect() as conn:
            result = conn.execute(check_query, {"tenant_id": tenant_id})
            existing_id = result.scalar()

            if existing_id:
                # Update existing record
                update_query = text("""
                    UPDATE restaurant_data
                    SET data = :data
                    WHERE id = :id
                    RETURNING id
                """)
                # Ensure data is properly serialized as JSON
                data_json = json.dumps(data) if not isinstance(data, str) else data
                result = conn.execute(
                    update_query,
                    {"id": existing_id, "data": data_json}
                )
                conn.commit()
                return result.scalar()
            else:
                # Insert new record
                insert_query = text("""
                    INSERT INTO restaurant_data (tenant_id, data)
                    VALUES (:tenant_id, :data)
                    RETURNING id
                """)
                # Ensure data is properly serialized as JSON
                data_json = json.dumps(data) if not isinstance(data, str) else data
                result = conn.execute(
                    insert_query,
                    {"tenant_id": tenant_id, "data": data_json}
                )
                conn.commit()
                return result.scalar()
    except Exception as e:
        print(f"Error saving restaurant data to database: {str(e)}")
        return None

async def get_restaurant_data(tenant_id: str) -> Optional[Dict[str, Any]]:
    """
    Get restaurant data for a tenant.

    Args:
        tenant_id: The tenant ID

    Returns:
        The restaurant data as a dictionary, or None if not found
    """
    query = text("""
        SELECT data, created_at, updated_at
        FROM restaurant_data
        WHERE tenant_id = :tenant_id
    """)

    try:
        with engine.connect() as conn:
            result = conn.execute(query, {"tenant_id": tenant_id})
            row = result.fetchone()

            if row:
                if isinstance(row.data, dict):
                    data = row.data
                else:
                    try:
                        data = json.loads(row.data)
                    except (TypeError, json.JSONDecodeError) as e:
                        print(f"Error parsing JSON data: {str(e)}")
                        print(f"Data type: {type(row.data)}")
                        print(f"Data value: {str(row.data)[:200]}...")
                        return None

                # Add metadata
                data["_metadata"] = {
                    "created_at": row.created_at.isoformat(),
                    "updated_at": row.updated_at.isoformat()
                }
                return data
            return None
    except Exception as e:
        print(f"Error retrieving restaurant data: {str(e)}")
        return None

async def delete_restaurant_data(tenant_id: str) -> bool:
    """
    Delete restaurant data for a tenant.

    Args:
        tenant_id: The tenant ID

    Returns:
        True if successful, False otherwise
    """
    query = text("""
        DELETE FROM restaurant_data
        WHERE tenant_id = :tenant_id
        RETURNING id
    """)

    try:
        with engine.connect() as conn:
            result = conn.execute(query, {"tenant_id": tenant_id})
            deleted_id = result.scalar()
            conn.commit()
            return deleted_id is not None
    except Exception as e:
        print(f"Error deleting restaurant data: {str(e)}")
        return False

async def list_restaurant_data(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """
    List restaurant data for all tenants with pagination.

    Args:
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        A list of restaurant data records
    """
    query = text("""
        SELECT id, tenant_id, data, created_at, updated_at
        FROM restaurant_data
        ORDER BY updated_at DESC
        LIMIT :limit OFFSET :offset
    """)

    try:
        with engine.connect() as conn:
            result = conn.execute(query, {"limit": limit, "offset": offset})

            records = []
            for row in result:
                data = json.loads(row.data)
                records.append({
                    "id": row.id,
                    "tenant_id": row.tenant_id,
                    "data": data,
                    "created_at": row.created_at.isoformat(),
                    "updated_at": row.updated_at.isoformat()
                })

            return records
    except Exception as e:
        print(f"Error listing restaurant data: {str(e)}")
        return []
