<div

>
<!-- cdkOverlayOrigin
#trigger="cdkOverlayOrigin"
(mouseover)="isHover = !isHover"
(mouseout)="isHover = !isHover" -->
  <img *ngIf="user.picture; else noPicture" src="{{ user.value.picture }}" />

  <ng-template #noPicture>
    <div class="circle" [ngStyle]="{ 'background-color': user.value.color }">
      <p>{{ initials }}</p>
    </div>
  </ng-template>
</div>
<!-- <ng-template
  *ngIf="hover"
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="trigger"
  [cdkConnectedOverlayOpen]="isHover"
>
  <app-user-popover [user]="user"></app-user-popover>
</ng-template> -->
