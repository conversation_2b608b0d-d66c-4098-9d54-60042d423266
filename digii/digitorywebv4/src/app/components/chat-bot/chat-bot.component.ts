import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { ConfirmDialogComponent } from './confirm-dialog/confirm-dialog.component';
import { SafeHtmlPipe } from '../../pipes/safe-html.pipe';
import { MarkdownPipe } from '../../pipes/markdown.pipe';
import { SseService } from 'src/app/services/sse.service';
import { ChatMessage } from 'src/app/models/chat-message.model';
import { Subscription } from 'rxjs';
import { EmptyStateComponent } from '../empty-state/empty-state.component';

@Component({
  selector: 'app-chat-bot',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatDialogModule,
    SafeHtmlPipe,
    MarkdownPipe,
    ConfirmDialogComponent,
    EmptyStateComponent
  ],
  templateUrl: './chat-bot.component.html',
  styleUrls: ['./chat-bot.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChatBotComponent implements OnInit, OnDestroy, OnChanges {
  @Input() tenantId: string = '';
  @Input() tenantName: string = '';
  @ViewChild('messageInput') messageInput!: ElementRef;

  messages: ChatMessage[] = [];
  currentMessage: string = '';
  isConnecting: boolean = false;
  isWaitingForResponse: boolean = false;
  restaurantData: any = null;
  conversationStarted: boolean = false;
  isRefreshing: boolean = false;

  private messageSubscription: Subscription | null = null;
  private connectionSubscription: Subscription | null = null;
  private dataUpdateSubscription: Subscription | null = null;

  constructor(
    private sseService: SseService,
    private cd: ChangeDetectorRef,
    private snackBar: MatSnackBar,
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['tenantId']) {
      const newTenantId = changes['tenantId'].currentValue;
      const prevTenantId = changes['tenantId'].previousValue;
      if (newTenantId && prevTenantId !== newTenantId) {
        this.loadingHistory = false;
        this.loadConversationHistory();
      }
    }
  }

  private loadingHistory = false;

  ngOnInit(): void {
    this.messages = [];
    if (this.tenantId) {
      this.loadingHistory = false;
      const conversationStartedKey = `conversation_started_${this.tenantId}`;
      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';
      this.loadConversationHistory();

      setTimeout(() => {
        if (this.messages.length > 0) {
          this.conversationStarted = true;
          localStorage.setItem(conversationStartedKey, 'true');
        } else if (this.conversationStarted) {
          this.initiateConversation();
        }
        this.cd.detectChanges();
        this.scrollToBottom();
      }, 2000);
    } else {
      this.messages = [];
    }

    this.messageSubscription = this.sseService.messages$.subscribe(
      (message: ChatMessage) => {
        if (message.sender === 'system') {
          if (message.id.startsWith('completion-')) {
            this.isWaitingForResponse = false;
            this.cd.detectChanges();
            return;
          }
          return;
        }

        if (message.sender === 'bot') {
          if (!message.text.trim()) {
            return;
          }
          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);

          if (existingMessageIndex !== -1) {
            if (message.text !== 'DIGI is thinking...') {
              this.messages[existingMessageIndex] = message;
            }
          } else {
            const duplicateMessage = this.messages.find(m =>
              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')
            );

            const thinkingIndex = this.messages.findIndex(m =>
              m.sender === 'bot' && (m.text === 'DIGI is thinking...' || m.text.includes('thinking')) && m.id === message.id
            );

            if (thinkingIndex !== -1) {
              this.messages[thinkingIndex] = message;
            } else if (!duplicateMessage && !message.text.includes('thinking')) {
              this.messages.push(message);
              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
            }
          }

          if (!message.text.includes('blinking-cursor')) {
            this.isWaitingForResponse = false;
          }

          if (message.text.includes('thinking')) {
            this.isWaitingForResponse = true;
            this.cd.detectChanges();
          }

          this.cd.detectChanges();
          this.scrollToBottom();
        } else if (message.sender === 'user') {
          const isDuplicate = this.messages.some(m =>
            m.sender === 'user' &&
            m.text === message.text &&
            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000
          );

          if (!isDuplicate) {
            this.messages.push(message);
            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
          } else {
          }
        }
        this.cd.detectChanges();
        this.scrollToBottom();
      }
    );

    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(
      (data: any) => {
        if (data && data.type === 'restaurant_data') {
          if (data.data) {
            this.restaurantData = data.data;
          } else {
            this.restaurantData = null;
          }
          setTimeout(() => {
            this.cd.detectChanges();
          }, 0);
        }
      }
    );
  }

  ngOnDestroy(): void {
    if (this.messageSubscription) {
      this.messageSubscription.unsubscribe();
    }

    if (this.connectionSubscription) {
      this.connectionSubscription.unsubscribe();
    }

    if (this.dataUpdateSubscription) {
      this.dataUpdateSubscription.unsubscribe();
    }
    this.sseService.disconnect();
  }


  sendMessage(): void {
    if (!this.currentMessage.trim()) {
      return;
    }

    if (!this.tenantId) {
      this.snackBar.open('Tenant ID is required to send messages', 'Close', {
        duration: 3000
      });
      return;
    }

    this.isConnecting = true;
    this.isWaitingForResponse = true;
    const messageToSend = this.currentMessage;
    this.currentMessage = '';
    this.cd.detectChanges();
    let messageType: 'text' | 'number' | 'short-answer' | 'system-message' = 'text';

    if (/^\d+$/.test(messageToSend.trim())) {
      messageType = 'number';
    }
    else if (messageToSend.trim().length <= 3 && !/^\d+$/.test(messageToSend.trim())) {
      messageType = 'short-answer';
    }

    const userMessage: ChatMessage = {
      id: this.generateId(),
      text: messageToSend,
      sender: 'user',
      timestamp: new Date(),
      messageType: messageType
    };

    const isDuplicate = this.messages.some(m =>
      m.sender === 'user' &&
      m.text === messageToSend
    );

    if (!isDuplicate) {
      this.messages.push(userMessage);
    }

    this.isWaitingForResponse = true;
    this.cd.detectChanges();
    this.scrollToBottom();

    setTimeout(() => {
      this.isWaitingForResponse = true;
      this.cd.detectChanges();
    }, 0);
    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({
      next: () => {
        this.isConnecting = false;
        this.cd.detectChanges();
      },
      error: (_error) => {
        this.isConnecting = false;
        this.isWaitingForResponse = false;
        this.snackBar.open('Failed to send message', 'Retry', {
          duration: 3000
        });
        this.cd.detectChanges();
      }
    });

    setTimeout(() => {
      this.messageInput?.nativeElement.focus();
    }, 0);
  }


  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  private scrollToBottom(): void {
    requestAnimationFrame(() => {
      const chatContainer = document.querySelector('.chat-messages');
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    });
  }


  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  trackById(_index: number, message: ChatMessage): string {
    return message.id;
  }


  loadRestaurantData(): void {
    if (!this.tenantId) {
      return;
    }

    this.sseService.fetchRestaurantData(this.tenantId).subscribe({
      next: (data) => {
        if (data) {
          this.restaurantData = data;
        } else {
          console.warn('No restaurant data available');
          this.restaurantData = null;
        }
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading restaurant data:', error);
        this.restaurantData = null;
        this.cd.detectChanges();
      }
    });
  }


  loadConversationHistory(): void {
    if (!this.tenantId || this.loadingHistory) {
      return;
    }

    this.loadingHistory = true;
    this.isConnecting = true;
    this.messages = [];

    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({
      next: (messages) => {
        if (messages && messages.length > 0) {
          messages.sort((a, b) => {
            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
          });
          this.messages = messages;
          this.conversationStarted = true;
          const conversationStartedKey = `conversation_started_${this.tenantId}`;
          localStorage.setItem(conversationStartedKey, 'true');
        } else {
          this.messages = [];
        }

        this.isConnecting = false;
        this.isWaitingForResponse = false;
        this.cd.detectChanges();
        this.scrollToBottom();
        this.loadRestaurantData();
      },
      error: (_error) => {
        this.isConnecting = false;
        this.isWaitingForResponse = false;
        this.messages = [];
        this.cd.detectChanges();
        this.loadRestaurantData();
      }
    });
  }

  clearConversationHistory(): void {
    this.isConnecting = true;
    this.cd.detectChanges();

    if (this.tenantId) {
      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({
        next: () => {
          this.messages = [];
          this.conversationStarted = false;
          const conversationStartedKey = `conversation_started_${this.tenantId}`;
          localStorage.removeItem(conversationStartedKey);
          this.restaurantData = null;
          this.loadingHistory = false;
          this.isConnecting = false;
          this.cd.detectChanges();
          this.scrollToBottom();
        },
        error: (_error) => {
          this.messages = [];
          this.conversationStarted = false;
          const conversationStartedKey = `conversation_started_${this.tenantId}`;
          localStorage.removeItem(conversationStartedKey);
          this.restaurantData = null;
          this.loadingHistory = false;
          this.isConnecting = false;
          this.cd.detectChanges();
          this.scrollToBottom();
        }
      });
    } else {
      this.messages = [
        {
          id: this.generateId(),
          text: 'Conversation has been cleared. How can I help you today?',
          sender: 'bot',
          timestamp: new Date()
        }
      ];

      this.loadingHistory = false;
      this.isConnecting = false;
      this.cd.detectChanges();
      this.scrollToBottom();
    }
  }


  startConversation(): void {
    if (!this.tenantId) {
      return;
    }
    this.conversationStarted = true;
    const conversationStartedKey = `conversation_started_${this.tenantId}`;
    localStorage.setItem(conversationStartedKey, 'true');
    this.initiateConversation();
    this.cd.detectChanges();
    this.scrollToBottom();
  }

  private initiateConversation(): void {
    if (!this.tenantId) {
      return;
    }
    this.isWaitingForResponse = true;
    this.cd.detectChanges();
    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({
      next: () => {
      },
      error: (_error) => {
        console.error('Error initiating conversation');
        this.isWaitingForResponse = false;
        this.cd.detectChanges();
        this.snackBar.open('Failed to continue conversation', 'Retry', {
          duration: 3000
        });
      }
    });
  }


  refreshRestaurantData(): void {
    if (!this.tenantId) {
      console.warn('Cannot refresh restaurant data: No tenant ID');
      this.snackBar.open('Cannot refresh: No tenant ID', 'Close', {
        duration: 3000
      });
      return;
    }

    if (this.isRefreshing) {
      return;
    }

    this.isRefreshing = true;
    this.cd.detectChanges();


    this.sseService.fetchRestaurantData(this.tenantId).subscribe({
      next: (data) => {
        if (data) {
          this.restaurantData = data;
          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {
            duration: 3000,
            panelClass: 'success-snackbar'
          });
        } else {
          console.warn('No restaurant data returned from API');
          this.snackBar.open('No restaurant data available', 'Close', {
            duration: 3000
          });
        }
        this.isRefreshing = false;
        this.cd.detectChanges();
      },
      error: (_error) => {
        console.error('Error refreshing restaurant data');
        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {
          duration: 3000,
          panelClass: 'error-snackbar'
        }).onAction().subscribe(() => {
          this.refreshRestaurantData();
        });
        this.isRefreshing = false;
        this.cd.detectChanges();
      }
    });
  }

  getCuisineItemCount(cuisine: string): number {
    if (!this.restaurantData || !this.restaurantData.cuisineMenuCounts) {
      return 0;
    }

    const cuisineCount = this.restaurantData.cuisineMenuCounts.find(
      (count: any) => count.cuisine.toLowerCase() === cuisine.toLowerCase()
    );

    return cuisineCount ? cuisineCount.menuItemCount : 0;
  }
}
