.dashboard-card {
  overflow: hidden;
}

.description {
  padding: 10px;
}

.content-container {
  position: relative;
  height: 300px;
  z-index: 1;
}

.image-container {
  position: absolute;
  z-index: 2;
  inset: 0;
  width: 100%;
  overflow: hidden;
  z-index: -1;
}

.image {
  object-fit: cover;
  object-position: center;
  width: 100%;
  transition-duration: 2s;
  transition-timing-function: ease-in;

  &:hover {
    transform: scale(1.05);

  }
}

.image-text {
  width: 100%;
  padding: 0.7rem 1rem;
  z-index: 1;
  text-transform: uppercase;
  font-weight: bold;
}

.extra-info {
  display: flex;
  justify-content: space-between;
  padding: 0.1rem 1.2rem;
}

.locations {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0.1rem;
}

.prices {
  display: flex;
  flex-direction: column;
  text-align: center;

  .small {
    font-size: 10px;
  }

  .bold {
    font-weight: bold;
    font-size: 16px;
  }
}