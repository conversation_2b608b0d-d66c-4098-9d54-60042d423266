import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import Chat from "@/pages/chat/";
import ChatWithID from "@/pages/chat/[chatId]";
import LoginForm from "./components/auth/LoginForm";
import { Toaster } from "@/components/ui/sonner";
import Home from "@/pages/Home";
import HomeLayout from "@/layout/HomeLayout";
import SettingsPage from "@/pages/Settings";
import SettingsLayout from "@/layout/SettingsLayout";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import HomeCOA from "@/pages/HomeCOA";
import UnauthorizedPage from "@/pages/Unauthorized";
import PermissionRoute from "@/components/auth/PermissionRoute";
import AuthLayout from "@/layout/AuthLayout";
import AuthPage from "@/pages/Auth";
import ConfirmPasswordPage from "@/pages/ConfirmPassword";
import { MASTER_PLAN_NAME } from "./constants";
import V2HomeLayout from "@/layout/v2/HomeLayout";
import V2Home from "@/pages/v2/Home";
import "@/v2.global.css";
import V2ChatLayout from "@/layout/v2/ChatLayout";
import V2Chat from "@/pages/v2/Chat";
import MasterPlans from "@/pages/v2/MasterPlans";
import V2SettingsLayout from "./layout/v2/SettingsLayout";
import AIChat from "@/pages/v2/AIChat";
import AIChatLayout from "@/layout/v2/AIChatLayout";
import PlansChat from "@/pages/v2/PlansChat";
import AccelaChat from "@/pages/v2/AccelaChat";
import Insights from "@/pages/v2/Insights";

function App() {
  return (
    <>
      <Routes>
        {/* Public routes that don't require authentication */}
        <Route path="/" element={<Navigate to="/v2/accela/chat" replace />} />
        <Route path="/v2" element={<Navigate to="/v2/accela/chat" replace />} />
        
        {/* Accela chat route - public access */}
        <Route path="/v2/accela/:chatId" element={<V2ChatLayout />}>
          <Route index element={<AccelaChat />} />
        </Route>
        
        {/* Protected routes that require authentication */}
        <Route element={<ProtectedRoute />}>
          <Route path="/home" element={<HomeLayout />}>
            <Route index element={<Home />} />
          </Route>
          <Route path={`/${MASTER_PLAN_NAME}`} element={<HomeLayout />}>
            <Route
              path={`/${MASTER_PLAN_NAME}/:applicationId/coas`}
              element={<PermissionRoute permissions="viewCoa" />}
            >
              <Route index element={<HomeCOA />} />
            </Route>
            <Route
              path={`/${MASTER_PLAN_NAME}/:applicationId/chats`}
              element={<Chat />}
            ></Route>
            <Route
              path={`/${MASTER_PLAN_NAME}/:applicationId/chats/:chatId`}
              element={<ChatWithID />}
            ></Route>
          </Route>
          <Route path="/settings" element={<SettingsLayout />}>
            <Route index element={<SettingsPage />} />
          </Route>
        </Route>
        
        <Route element={<ProtectedRoute />}>
          <Route path="/v2" element={<V2HomeLayout />}>
            <Route path="/v2/home" element={<V2Home />} />
            <Route path="/v2/master-plans" element={<MasterPlans />} />
            <Route path="/v2/insights" element={<Insights />} />
          </Route>
          <Route path="/v2/converse" element={<AIChatLayout />}>
            <Route path="chat" element={<AIChat />} />
          </Route>
          <Route path="/v2/chat/:chatId" element={<V2ChatLayout />}>
            <Route index element={<V2Chat />} />
          </Route>
          <Route path="/v2/plans/:chatId" element={<V2ChatLayout />}>
            <Route index element={<PlansChat />} />
          </Route>
          <Route path="/v2/settings" element={<V2SettingsLayout />}>
            <Route index element={<SettingsPage />} />
          </Route>
        </Route>
        
        <Route path="/signup" element={<LoginForm />} />
        <Route path="/" element={<AuthLayout />}>
          <Route path="/login" element={<AuthPage />} />
          <Route path="/forgot-password" element={<AuthPage />} />
          <Route path="/confirm-password" element={<ConfirmPasswordPage />} />
        </Route>
        <Route path="/unauthorized" element={<UnauthorizedPage />} />

        {/* V2 routes */}
      </Routes>
      <Toaster theme="light" position="bottom-right" expand richColors />
    </>
  );
}

export default App;
