import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, BarChart3, Search, Zap } from 'lucide-react';

interface AILoaderProps {
  message?: string;
  className?: string;
}

const AILoader: React.FC<AILoaderProps> = ({ 
  message = "Generating insights...", 
  className = "" 
}) => {
  return (
    <div className={`flex flex-col items-center justify-center py-16 ${className}`}>
      {/* Main animated container */}
      <div className="relative mb-8">
        {/* Outer rotating ring */}
        <motion.div
          className="w-24 h-24 border-4 border-blue-100 rounded-full"
          animate={{ rotate: 360 }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Inner rotating ring */}
        <motion.div
          className="absolute inset-2 w-20 h-20 border-4 border-blue-200 border-t-blue-500 rounded-full"
          animate={{ rotate: -360 }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Center brain icon */}
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          animate={{ 
            scale: [1, 1.1, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Brain className="w-8 h-8 text-blue-600" />
        </motion.div>
      </div>

      {/* Floating icons */}
      <div className="relative w-32 h-16 mb-6">
        <motion.div
          className="absolute left-0 top-0"
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 0
          }}
        >
          <BarChart3 className="w-5 h-5 text-blue-400" />
        </motion.div>
        
        <motion.div
          className="absolute right-0 top-0"
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 0.5
          }}
        >
          <Search className="w-5 h-5 text-green-400" />
        </motion.div>
        
        <motion.div
          className="absolute left-1/2 bottom-0 transform -translate-x-1/2"
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 1
          }}
        >
          <Zap className="w-5 h-5 text-yellow-400" />
        </motion.div>
      </div>

      {/* Animated text */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          Accela AI is working
        </h3>
        <motion.p
          className="text-gray-600"
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          {message}
        </motion.p>
      </motion.div>

      {/* Progress dots */}
      <div className="flex space-x-2 mt-6">
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className="w-2 h-2 bg-blue-400 rounded-full"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: index * 0.2
            }}
          />
        ))}
      </div>

      {/* Subtle background animation */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-blue-50/20 via-transparent to-green-50/20 rounded-lg -z-10"
        animate={{
          opacity: [0.3, 0.6, 0.3]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  );
};

export default AILoader;
