import React from 'react';
import {
  <PERSON>C<PERSON>,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Chart } from '@/services/dashboardService';

interface ChartRendererProps {
  chart: Chart;
  className?: string;
}

const ChartRenderer: React.FC<ChartRendererProps> = ({ chart, className = '' }) => {
  // Transform data for Recharts format
  const transformDataForRecharts = () => {
    const { labels, datasets } = chart.data;
    
    if (chart.type === 'pie' || chart.type === 'doughnut') {
      // For pie charts, create array of objects with name and value
      return labels.map((label, index) => ({
        name: label,
        value: datasets[0]?.data[index] || 0,
        fill: datasets[0]?.backgroundColor?.[index] || `hsl(${index * 45}, 70%, 50%)`
      }));
    } else {
      // For other charts, create objects with label as key and dataset values
      return labels.map((label, index) => {
        const dataPoint: any = { name: label };
        datasets.forEach((dataset, datasetIndex) => {
          dataPoint[dataset.label] = dataset.data[index] || 0;
        });
        return dataPoint;
      });
    }
  };

  const data = transformDataForRecharts();
  const colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'];

  const renderChart = () => {
    switch (chart.type) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {chart.data.datasets.map((dataset, index) => (
                <Bar
                  key={dataset.label}
                  dataKey={dataset.label}
                  fill={colors[index % colors.length]}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {chart.data.datasets.map((dataset, index) => (
                <Line
                  key={dataset.label}
                  type="monotone"
                  dataKey={dataset.label}
                  stroke={colors[index % colors.length]}
                  strokeWidth={2}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {chart.data.datasets.map((dataset, index) => (
                <Area
                  key={dataset.label}
                  type="monotone"
                  dataKey={dataset.label}
                  stackId="1"
                  stroke={colors[index % colors.length]}
                  fill={colors[index % colors.length]}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'pie':
      case 'doughnut':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={chart.type === 'doughnut' ? 60 : 0}
                outerRadius={100}
                paddingAngle={2}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        );

      case 'table':
        return (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  {chart.data.headers?.map((header, index) => (
                    <th key={index} className="text-left p-2 font-medium">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {chart.data.rows?.map((row, rowIndex) => (
                  <tr key={rowIndex} className="border-b">
                    {row.map((cell: any, cellIndex: number) => (
                      <td key={cellIndex} className="p-2">
                        {cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      default:
        return (
          <div className="flex items-center justify-center h-64 text-gray-500">
            Unsupported chart type: {chart.type}
          </div>
        );
    }
  };

  const getSizeClass = () => {
    switch (chart.size) {
      case 'small':
        return 'col-span-1';
      case 'medium':
        return 'col-span-1 md:col-span-2';
      case 'large':
        return 'col-span-1 md:col-span-2 lg:col-span-3';
      default:
        return 'col-span-1 md:col-span-2';
    }
  };

  return (
    <Card className={`${getSizeClass()} ${className}`}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          {chart.title}
        </CardTitle>
        {chart.subtitle && (
          <p className="text-sm text-gray-600">
            {chart.subtitle}
          </p>
        )}
      </CardHeader>
      <CardContent>
        {renderChart()}
      </CardContent>
    </Card>
  );
};

export default ChartRenderer;
