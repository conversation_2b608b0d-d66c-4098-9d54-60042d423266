import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Search, BarChart3 } from 'lucide-react';
import SummaryCard from './SummaryCard';
import ChartRenderer from './ChartRenderer';
import dashboardService, { 
  DashboardConfig, 
  DashboardResponse, 
  SmartDashboardRequest,
  DashboardType 
} from '@/services/dashboardService';

interface DashboardProps {
  className?: string;
}

const Dashboard: React.FC<DashboardProps> = ({ className = '' }) => {
  // State management
  const [config, setConfig] = useState<DashboardConfig | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [selectedDashboardType, setSelectedDashboardType] = useState('permits');
  const [userQuery, setUserQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'default' | 'ask'>('default');

  // Load configuration on mount
  useEffect(() => {
    loadDashboardConfig();
  }, []);

  // Load default dashboard when dashboard type changes
  useEffect(() => {
    if (config && activeTab === 'default') {
      loadDefaultDashboard();
    }
  }, [selectedDashboardType, config, activeTab]);

  const loadDashboardConfig = async () => {
    try {
      const response = await dashboardService.getConfig();
      if (response.status === 'success') {
        setConfig(response.data);
        setSelectedDashboardType(response.data.ui_config.default_dashboard_type);
      }
    } catch (err) {
      console.error('Failed to load dashboard config:', err);
      setError('Failed to load dashboard configuration');
    }
  };

  const loadDefaultDashboard = async () => {
    if (!config) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const request = dashboardService.createDashboardRequest(
        selectedDashboardType,
        '',
        true
      );
      
      const response = await dashboardService.getDefaultDashboardData(request);
      
      if (response.status === 'success' && response.data) {
        setDashboardData(response.data);
      } else {
        setError(response.message || 'Failed to load dashboard data');
      }
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleAskAccelaAI = async () => {
    if (!userQuery.trim() || !config) return;

    setLoading(true);
    setError(null);
    setDashboardData(null);

    try {
      const request = dashboardService.createDashboardRequest(
        selectedDashboardType,
        userQuery,
        false
      );

      // Use streaming for Ask Accela AI
      await dashboardService.getSmartDashboardDataStreaming(
        request,
        (event) => {
          // Handle streaming events (could show progress indicators)
          console.log('Dashboard event:', event);
        },
        (data) => {
          // Handle completion with dashboard data
          setDashboardData(data);
          setLoading(false);
        },
        (error) => {
          // Handle errors
          setError(error);
          setLoading(false);
        }
      );
    } catch (err) {
      console.error('Failed to ask Accela AI:', err);
      setError('Failed to generate dashboard from query');
      setLoading(false);
    }
  };

  const getDashboardTypeInfo = (type: string): DashboardType | undefined => {
    return config?.dashboard_types.find(dt => dt.value === type);
  };

  if (!config) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dashboard...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Accela Insights</h1>
          <p className="text-gray-600 mt-1">
            Smart dashboard for {getDashboardTypeInfo(selectedDashboardType)?.label || 'Accela'} data
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Select value={selectedDashboardType} onValueChange={setSelectedDashboardType}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select dashboard type" />
            </SelectTrigger>
            <SelectContent>
              {config.dashboard_types.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Tabs for Default vs Ask Accela AI */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'default' | 'ask')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="default" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Default Dashboard
          </TabsTrigger>
          <TabsTrigger value="ask" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Ask Accela AI
          </TabsTrigger>
        </TabsList>

        <TabsContent value="default" className="space-y-6">
          {/* Default Dashboard Content */}
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading dashboard data...</span>
            </div>
          ) : error ? (
            <Card>
              <CardContent className="p-6">
                <div className="text-center text-red-600">
                  <p>{error}</p>
                  <Button onClick={loadDefaultDashboard} className="mt-4">
                    Retry
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : dashboardData ? (
            <>
              {/* Summary Cards */}
              {dashboardData.summary_items.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {dashboardData.summary_items.map((item, index) => (
                    <SummaryCard key={index} item={item} />
                  ))}
                </div>
              )}

              {/* Charts */}
              {dashboardData.charts.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {dashboardData.charts.map((chart) => (
                    <ChartRenderer key={chart.id} chart={chart} />
                  ))}
                </div>
              )}
            </>
          ) : null}
        </TabsContent>

        <TabsContent value="ask" className="space-y-6">
          {/* Ask Accela AI Interface */}
          <Card>
            <CardHeader>
              <CardTitle>Ask Accela AI</CardTitle>
              <p className="text-sm text-gray-600">
                Ask natural language questions about your {getDashboardTypeInfo(selectedDashboardType)?.label.toLowerCase()} data
              </p>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Input
                  placeholder="e.g., Show me permit trends for the last 6 months"
                  value={userQuery}
                  onChange={(e) => setUserQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAskAccelaAI()}
                  className="flex-1"
                />
                <Button 
                  onClick={handleAskAccelaAI}
                  disabled={loading || !userQuery.trim()}
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  Ask AI
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* AI Response */}
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Generating insights...</span>
            </div>
          ) : error ? (
            <Card>
              <CardContent className="p-6">
                <div className="text-center text-red-600">
                  <p>{error}</p>
                </div>
              </CardContent>
            </Card>
          ) : dashboardData ? (
            <>
              {/* Summary Cards */}
              {dashboardData.summary_items.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {dashboardData.summary_items.map((item, index) => (
                    <SummaryCard key={index} item={item} />
                  ))}
                </div>
              )}

              {/* Charts */}
              {dashboardData.charts.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {dashboardData.charts.map((chart) => (
                    <ChartRenderer key={chart.id} chart={chart} />
                  ))}
                </div>
              )}
            </>
          ) : null}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
