import React from 'react';
import { motion } from 'framer-motion';
import { BarChart3, Pie<PERSON><PERSON>, TrendingUp, Activity, Database } from 'lucide-react';

interface DashboardLoaderProps {
  message?: string;
  dashboardType?: string;
  className?: string;
}

const DashboardLoader: React.FC<DashboardLoaderProps> = ({ 
  message = "Loading dashboard data...", 
  dashboardType = "dashboard",
  className = "" 
}) => {
  const getDashboardIcon = () => {
    switch (dashboardType) {
      case 'permits': return Database;
      case 'inspections': return Activity;
      case 'code_enforcement': return TrendingUp;
      case 'licensing': return PieChart;
      default: return BarChart3;
    }
  };

  const DashboardIcon = getDashboardIcon();

  return (
    <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
      {/* Main chart loading animation */}
      <div className="relative mb-8">
        {/* Outer pulse ring */}
        <motion.div
          className="w-20 h-20 border-2 border-slate-200 rounded-lg"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Inner rotating square */}
        <motion.div
          className="absolute inset-2 w-16 h-16 border-2 border-blue-600 rounded-lg"
          animate={{ rotate: 360 }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Center dashboard icon */}
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          animate={{
            scale: [0.8, 1, 0.8],
            opacity: [0.6, 1, 0.6]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <DashboardIcon className="w-7 h-7 text-blue-600" />
        </motion.div>
      </div>

      {/* Animated chart bars */}
      <div className="flex items-end space-x-1 mb-6">
        {[0, 1, 2, 3, 4].map((index) => (
          <motion.div
            key={index}
            className="w-3 bg-blue-600 rounded-t"
            animate={{
              height: [16, 32, 20, 28, 16],
              opacity: [0.6, 1, 0.8, 1, 0.6]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: index * 0.2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Loading text */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          Loading Dashboard
        </h3>
        <motion.p
          className="text-gray-600 text-sm"
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          {message}
        </motion.p>
      </motion.div>

      {/* Data processing indicators */}
      <div className="flex items-center space-x-4 mt-6">
        <motion.div
          className="flex items-center space-x-2 text-xs text-gray-500"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 0
          }}
        >
          <div className="w-2 h-2 bg-emerald-600 rounded-full" />
          <span>Processing data</span>
        </motion.div>

        <motion.div
          className="flex items-center space-x-2 text-xs text-gray-500"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 0.7
          }}
        >
          <div className="w-2 h-2 bg-blue-600 rounded-full" />
          <span>Generating charts</span>
        </motion.div>

        <motion.div
          className="flex items-center space-x-2 text-xs text-gray-500"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 1.4
          }}
        >
          <div className="w-2 h-2 bg-violet-600 rounded-full" />
          <span>Calculating KPIs</span>
        </motion.div>
      </div>

      {/* Subtle shimmer effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-lg -z-10"
        animate={{
          x: [-100, 300]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </div>
  );
};

export default DashboardLoader;
