import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import {
  Building,
  DollarSign,
  Hash,
  Percent,
  Info,
  Calendar,
  Search,
  CheckCircle,
  XCircle,
  Shield,
  AlertTriangle,
  Award
} from 'lucide-react';
import { SummaryItem } from '@/types/dashboard';

interface SummaryCardProps {
  item: SummaryItem;
  className?: string;
}

const iconMap: Record<string, React.ComponentType<any>> = {
  'building': Building,
  'dollar-sign': DollarSign,
  'hash': Hash,
  'percent': Percent,
  'info': Info,
  'calendar': Calendar,
  'search': Search,
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  'shield': Shield,
  'alert-triangle': AlertTriangle,
  'certificate': Award,
};

const colorMap: Record<string, string> = {
  currency: '#059669',
  number: '#2563EB',
  percentage: '#D97706',
  text: '#6B7280',
  days: '#7C3AED',
};

const SummaryCard: React.FC<SummaryCardProps> = ({ item, className = '' }) => {
  const IconComponent = iconMap[item.icon] || Info;
  const cardColor = colorMap[item.data_type] || '#6B7280';

  return (
    <Card className={`relative overflow-hidden ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <div 
                className="p-2 rounded-lg"
                style={{ backgroundColor: `${cardColor}20` }}
              >
                <IconComponent 
                  className="h-5 w-5" 
                  style={{ color: cardColor }}
                />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 mb-1">
                  {item.label}
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {item.value}
                </p>
              </div>
            </div>
            
            {item.secondary_info && (
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500">
                  {item.secondary_info}
                </span>
              </div>
            )}
            
            {item.formula && (
              <div className="mt-2 text-xs text-gray-400 font-mono">
                {item.formula}
              </div>
            )}
          </div>
        </div>
        
        {/* Decorative accent */}
        <div 
          className="absolute top-0 right-0 w-1 h-full"
          style={{ backgroundColor: cardColor }}
        />
      </CardContent>
    </Card>
  );
};

export default SummaryCard;
