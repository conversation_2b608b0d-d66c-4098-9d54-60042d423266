import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import NotificationsDropdown from "@/components/v2/navbar/NotificationsDropdown";
import ProfileDropdown from "@/components/v2/navbar/ProfileDropdown";
import { useUser } from "@/hooks/useUser";
import { Button } from "@/components/ui/button";
import { FolderArchive, Home, ChartArea, Database, BarChart3 } from "lucide-react";
export { NavigationBar };

interface NavigationBarProps {
  userName?: string;
  onTabChange?: (tab: "home" | "master-plans" | "permit-analysis" | "accela" | "insights") => void;
  hasNotifications?: boolean;
  onLogout?: () => void;
}

const NavigationBar: React.FC<NavigationBarProps> = ({ onTabChange }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, greetUser } = useUser();

  // Hide navbar if route is /v2/accela/chat
  if (location.pathname === "/v2/accela/chat") {
    return null;
  }

  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes("master-plans")) return "master-plans";
    if (path.includes("home")) return "home";
    if (path.includes("insights")) return "insights";
    if (path === "/v2/plans/chat") return "permit-analysis";
    if (path.includes("permit-analysis")) return "permit-analysis";
    if (path === "/v2/accela/chat") return "accela";
    if (path.includes("accela")) return "accela";
    return "";
  };

  const handleTabClick = (tab: "home" | "master-plans" | "permit-analysis" | "accela" | "insights") => {
    const path = tab === "home" ? "/v2/home" :
                 tab === "master-plans" ? "/v2/master-plans" :
                 tab === "insights" ? "/v2/insights" :
                 tab === "permit-analysis" ? "/v2/plans/chat" :
                 "/v2/accela/chat";
    navigate(path);
    onTabChange?.(tab);
  };

  const activeTab = getActiveTab();

  return (
    <nav className="grid grid-cols-2 sm:grid-cols-3 items-center px-6 py-4 bg-white-color shadow-sm relative pl-20">
      {/* Left section - County name (desktop only) */}
      <div className="hidden sm:block">
        {/* <div className="text-lg font-bold">{user?.county}</div> */}
      </div>

      {/* Navigation buttons */}
      <div className="flex gap-4 sm:gap-8 sm:justify-self-center">
        <Button
          variant="ghost"
          className={`text-base rounded-2xl hover:text-brand-500 hover:bg-brand-50 inline-flex gap-1 ${
            activeTab === "home"
              ? "text-brand-500 bg-brand-50"
              : "text-zinc-600 bg-zinc-100/65"
          }`}
          onClick={() => handleTabClick("home")}
        >
          <Home size={18} strokeWidth={2.5} /> Home
        </Button>
        <Button
          variant="ghost"
          className={`text-base rounded-2xl hover:text-brand-500 hover:bg-brand-50 inline-flex gap-1  ${
            activeTab === "master-plans"
              ? "text-brand-500 bg-brand-50 "
              : "text-zinc-600 bg-zinc-100/65"
          }`}
          onClick={() => handleTabClick("master-plans")}
        >
          <FolderArchive size={18} strokeWidth={2.5} />
          Projects
        </Button>
        <Button
          variant="ghost"
          className={`text-base rounded-2xl hover:text-brand-500 hover:bg-brand-50 inline-flex gap-1  ${
            activeTab === "insights"
              ? "text-brand-500 bg-brand-50 "
              : "text-zinc-600 bg-zinc-100/65"
          }`}
          onClick={() => handleTabClick("insights")}
        >
          <BarChart3 size={18} strokeWidth={2.5} />
          Insights
        </Button>
        <Button
          variant="ghost"
          className={`text-base rounded-2xl hover:text-brand-500 hover:bg-brand-50 inline-flex gap-1  ${
            activeTab === "permit-analysis"
              ? "text-brand-500 bg-brand-50 "
              : "text-zinc-600 bg-zinc-100/65"
          }`}
          onClick={() => {
            navigate("/v2/plans/chat");
            onTabChange?.("permit-analysis" as any);
          }}
        >
          <ChartArea size={18} strokeWidth={2.5} />
          Permit Analysis
        </Button>
        <Button
          variant="ghost"
          className={`text-base rounded-2xl hover:text-brand-500 hover:bg-brand-50 inline-flex gap-1  ${
            activeTab === "accela"
              ? "text-brand-500 bg-brand-50 "
              : "text-zinc-600 bg-zinc-100/65"
          }`}
          onClick={() => handleTabClick("accela")}
        >
          <Database size={18} strokeWidth={2.5} />
          Accela KB
        </Button>
      </div>

      {/* Right section - Profile and notifications */}
      <div className="justify-self-end flex items-center gap-4">
        <div className="text-base text-gray-600 hidden lg:block">
          {greetUser()}
        </div>
        <NotificationsDropdown />
        <ProfileDropdown />
      </div>
    </nav>
  );
};

export default NavigationBar;
