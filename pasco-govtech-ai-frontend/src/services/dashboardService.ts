import axios from 'axios';

// ===== INTERFACES =====
export interface DashboardFilters {
  counties: string[];
  start_date: string;
  end_date: string;
  base_date: string;
  categories: string[];
  subcategories: string[];
  departments: string[];
}

export interface SmartDashboardRequest {
  filters: DashboardFilters;
  user_query: string;
  use_default_charts: boolean;
  dashboard_type: string;
}

export interface SummaryItem {
  icon: string;
  value: string;
  label: string;
  data_type: string;
  secondary_info?: string;
  formula?: string;
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string[];
  borderColor?: string[];
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
  headers?: string[];  // For table charts
  rows?: any[];        // For table charts
}

export interface Chart {
  id: string;
  title: string;
  type: string;
  data: ChartData;
  subtitle?: string;
  size?: string;
  options?: any;
}

export interface DashboardResponse {
  charts: Chart[];
  summary_items: SummaryItem[];
}

export interface SmartDashboardApiResponse {
  status: string;
  data?: DashboardResponse;
  message?: string;
  error_code?: string;
}

export interface DashboardType {
  value: string;
  label: string;
  icon: string;
  description: string;
}

export interface BaseDateOption {
  value: string;
  label: string;
}

export interface DashboardConfig {
  chart_colors: string[];
  chart_types: Record<string, string>;
  currency: {
    code: string;
    symbol: string;
  };
  dashboard_types: DashboardType[];
  base_date_options: BaseDateOption[];
  default_chart_options: Record<string, any>;
  summary_card_config: {
    colors: Record<string, string>;
    icons: Record<string, string>;
  };
  ui_config: {
    default_date_range_days: number;
    default_dashboard_type: string;
    default_base_date: string;
  };
}

export interface DashboardConfigResponse {
  status: string;
  data: DashboardConfig;
}

// ===== SERVICE =====
class DashboardService {
  private readonly baseUrl = 'http://localhost:8001'; // Accela backend URL
  
  // ===== API METHODS =====
  /**
   * Get dashboard configuration
   */
  async getConfig(): Promise<DashboardConfigResponse> {
    const response = await axios.get(`${this.baseUrl}/dashboard/config`);
    return response.data;
  }

  /**
   * Get smart dashboard data with streaming using fetch EventSource
   */
  async getSmartDashboardDataStreaming(
    request: SmartDashboardRequest,
    onEvent: (event: any) => void,
    onComplete: (data: DashboardResponse) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/dashboard/smart_ask`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData = JSON.parse(line.slice(6));
              onEvent(eventData);

              // Check if this is the final dashboard data
              if (eventData.event_type === 'dashboard_complete') {
                onComplete(eventData.data);
              } else if (eventData.event_type === 'error') {
                onError(eventData.data);
                return;
              }
            } catch (e) {
              console.warn('Failed to parse event data:', line);
            }
          }
        }
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Unknown error occurred');
    }
  }

  /**
   * Get default dashboard data (non-streaming)
   */
  async getDefaultDashboardData(request: SmartDashboardRequest): Promise<SmartDashboardApiResponse> {
    const response = await axios.post(`${this.baseUrl}/dashboard/default`, request);
    return response.data;
  }

  // ===== UTILITY METHODS =====
  /**
   * Format currency values
   */
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }

  /**
   * Format number values
   */
  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-US').format(value);
  }

  /**
   * Get icon for summary card based on data type
   */
  getSummaryCardIcon(dataType: string, label: string): string {
    const iconMap: Record<string, string> = {
      currency: 'dollar-sign',
      number: 'hash',
      percentage: 'percent',
      text: 'info',
      days: 'calendar',
    };
    return iconMap[dataType] || 'info';
  }

  /**
   * Get color for summary card based on data type
   */
  getSummaryCardColor(dataType: string): string {
    const colorMap: Record<string, string> = {
      currency: '#10B981',
      number: '#3B82F6',
      percentage: '#F59E0B',
      text: '#6B7280',
      days: '#8B5CF6',
    };
    return colorMap[dataType] || '#6B7280';
  }

  /**
   * Create default filters for dashboard requests
   */
  createDefaultFilters(): DashboardFilters {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 30); // Last 30 days

    return {
      counties: [],
      start_date: startDate.toISOString().split('T')[0]!,
      end_date: endDate.toISOString().split('T')[0]!,
      base_date: 'application_date',
      categories: [],
      subcategories: [],
      departments: [],
    };
  }

  /**
   * Create dashboard request object
   */
  createDashboardRequest(
    dashboardType: string,
    userQuery = '',
    useDefaultCharts = true,
    filters?: Partial<DashboardFilters>
  ): SmartDashboardRequest {
    const defaultFilters = this.createDefaultFilters();
    
    return {
      filters: { ...defaultFilters, ...filters },
      user_query: userQuery,
      use_default_charts: useDefaultCharts,
      dashboard_type: dashboardType,
    };
  }
}

export const dashboardService = new DashboardService();
export default dashboardService;
