import axios from 'axios';
import {
  DashboardFilters,
  SmartDashboardRequest,
  SmartDashboardApiResponse,
  DashboardConfigResponse,
  DashboardResponse
} from '@/types/dashboard';
class DashboardService {
  private readonly baseUrl = process.env.REACT_APP_BACKEND_BASE_URL;
  async getConfig(): Promise<DashboardConfigResponse> {
    const response = await axios.get(`${this.baseUrl}/dashboard/config`);
    return response.data;
  }

  async getSmartDashboardDataStreaming(
    request: SmartDashboardRequest,
    onEvent: (event: any) => void,
    onComplete: (data: DashboardResponse) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/dashboard/smart_ask`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData = JSON.parse(line.slice(6));
              onEvent(eventData);

              if (eventData.event_type === 'dashboard_complete') {
                onComplete(eventData.data);
              } else if (eventData.event_type === 'error') {
                onError(eventData.data);
                return;
              }
            } catch (e) {
              console.warn('Failed to parse event data:', line);
            }
          }
        }
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Unknown error occurred');
    }
  }
  async getDefaultDashboardData(request: SmartDashboardRequest): Promise<SmartDashboardApiResponse> {
    const response = await axios.post(`${this.baseUrl}/dashboard/default`, request);
    return response.data;
  }


  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-US').format(value);
  }

  getSummaryCardIcon(dataType: string): string {
    const iconMap: Record<string, string> = {
      currency: 'dollar-sign',
      number: 'hash',
      percentage: 'percent',
      text: 'info',
      days: 'calendar',
    };
    return iconMap[dataType] || 'info';
  }

  getSummaryCardColor(dataType: string): string {
    const colorMap: Record<string, string> = {
      currency: '#10B981',
      number: '#3B82F6',
      percentage: '#F59E0B',
      text: '#6B7280',
      days: '#8B5CF6',
    };
    return colorMap[dataType] || '#6B7280';
  }

  createDefaultFilters(): DashboardFilters {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 30);

    return {
      counties: [],
      start_date: startDate.toISOString().split('T')[0]!,
      end_date: endDate.toISOString().split('T')[0]!,
      base_date: 'application_date',
      categories: [],
      subcategories: [],
      departments: [],
    };
  }


  createDashboardRequest(
    dashboardType: string,
    userQuery = '',
    useDefaultCharts = true,
    filters?: Partial<DashboardFilters>
  ): SmartDashboardRequest {
    const defaultFilters = this.createDefaultFilters();
    
    return {
      filters: { ...defaultFilters, ...filters },
      user_query: userQuery,
      use_default_charts: useDefaultCharts,
      dashboard_type: dashboardType,
    };
  }
}

export const dashboardService = new DashboardService();
export default dashboardService;
