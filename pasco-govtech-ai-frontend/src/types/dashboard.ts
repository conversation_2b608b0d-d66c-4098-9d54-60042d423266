export interface DashboardFilters {
  counties: string[];
  start_date: string;
  end_date: string;
  base_date: string;
  categories: string[];
  subcategories: string[];
  departments: string[];
}

export interface SmartDashboardRequest {
  filters: DashboardFilters;
  user_query: string;
  use_default_charts: boolean;
  dashboard_type: string;
}

export interface SummaryItem {
  icon: string;
  value: string;
  label: string;
  data_type: string;
  secondary_info?: string;
  formula?: string;
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string[];
  borderColor?: string[];
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
  headers?: string[];
  rows?: any[];
}

export interface Chart {
  id: string;
  title: string;
  type: string;
  data: ChartData;
  subtitle?: string;
  size?: string;
  options?: any;
}

export interface DashboardResponse {
  charts: Chart[];
  summary_items: SummaryItem[];
}

export interface SmartDashboardApiResponse {
  status: string;
  data?: DashboardResponse;
  message?: string;
  error_code?: string;
}

export interface DashboardType {
  value: string;
  label: string;
  icon: string;
  description: string;
}

export interface BaseDateOption {
  value: string;
  label: string;
}

export interface DashboardConfig {
  chart_colors: string[];
  chart_types: Record<string, string>;
  currency: {
    code: string;
    symbol: string;
  };
  dashboard_types: DashboardType[];
  base_date_options: BaseDateOption[];
  default_chart_options: Record<string, any>;
  summary_card_config: {
    colors: Record<string, string>;
    icons: Record<string, string>;
  };
  ui_config: {
    default_date_range_days: number;
    default_dashboard_type: string;
    default_base_date: string;
  };
}

export interface DashboardConfigResponse {
  status: string;
  data: DashboardConfig;
}
